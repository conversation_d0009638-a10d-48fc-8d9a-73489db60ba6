#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from dynamic_gap_detector import analyze_market_competition, analyze_market_state, find_all_gap_points, calculate_dynamic_thresholds, comprehensive_evaluation, identify_leading_group

def test_stock_competition_debug():
    """调试个股资金流竞争激烈度检测问题"""
    print("=== 个股资金流竞争激烈度调试 ===")
    
    # 模拟用户提供的数据：亚太药业 2.11亿, 中电鑫龙 2.02亿, 誉衡药业 1.73亿
    test_inflows = [2.11e8, 2.02e8, 1.73e8, 1.5e8, 1.2e8]  # 转换为元
    test_names = ["亚太药业", "中电鑫龙", "誉衡药业", "股票4", "股票5"]
    
    print("测试数据:")
    for i, (name, amount) in enumerate(zip(test_names, test_inflows)):
        print(f"  {i+1}. {name}: {amount/1e8:.2f}亿")
    
    # 1. 测试竞争激烈度分析
    print("\n1. 竞争激烈度分析:")
    competition_info = analyze_market_competition(test_inflows, test_names)
    print(f"  是否竞争激烈: {competition_info['is_competitive']}")
    print(f"  竞争类型: {competition_info['competition_type']}")
    if competition_info.get('leading_group'):
        print(f"  领先集团: {competition_info['leading_group']}")
        print(f"  最大差距: {competition_info.get('max_ratio', 0):.3f}倍")
    
    # 计算具体的比例来调试
    print("\n  详细比例分析:")
    ratios = []
    for i in range(len(test_inflows)-1):
        if test_inflows[i+1] > 0:
            ratio = test_inflows[i] / test_inflows[i+1]
            ratios.append(ratio)
            print(f"    第{i+1}名/第{i+2}名: {test_inflows[i]/1e8:.2f}亿 / {test_inflows[i+1]/1e8:.2f}亿 = {ratio:.3f}倍")
    
    # 检查各种阈值条件
    print("\n  阈值检查:")
    if len(ratios) >= 1:
        print(f"    第1/2名比例: {ratios[0]:.3f} (阈值1.12: {'超过' if ratios[0] >= 1.12 else '未超过'})")
        print(f"    第1/2名比例: {ratios[0]:.3f} (阈值1.08: {'超过' if ratios[0] >= 1.08 else '未超过'})")
    
    if len(ratios) >= 2:
        print(f"    第2/3名比例: {ratios[1]:.3f} (阈值1.20: {'超过' if ratios[1] >= 1.20 else '未超过'})")
    
    # 2. 测试市场状态分析
    print("\n2. 市场状态分析:")
    market_state = analyze_market_state(test_inflows)
    print(f"  资金规模: {market_state['scale']}")
    print(f"  集中度: {market_state['concentration']:.1%}")
    print(f"  总计前5名: {market_state['total_top5']:.1f}亿")
    
    # 3. 测试断层点搜索
    print("\n3. 断层点搜索:")
    gap_scores, max_gap = find_all_gap_points(test_inflows)
    print(f"  最大断层位置: 第{max_gap['position']+1}名后")
    print(f"  绝对差距: {max_gap['abs_gap']/1e8:.2f}亿")
    print(f"  相对差距: {max_gap['rel_gap']:.3f}倍")
    print(f"  断层得分: {max_gap['score']:.2f}")
    
    # 4. 测试动态阈值
    print("\n4. 动态阈值:")
    thresholds = calculate_dynamic_thresholds(market_state)
    print(f"  最小相对差距阈值: {thresholds['min_relative_gap']:.2f}")
    print(f"  最小绝对差距阈值: {thresholds['min_absolute_gap']:.2f}亿")
    print(f"  最小综合得分阈值: {thresholds['min_gap_score']:.1f}")
    
    # 5. 测试集团识别
    print("\n5. 集团识别:")
    group_info = identify_leading_group(test_inflows, max_gap["position"], test_names)
    print(f"  领先集团大小: {group_info['size']}")
    print(f"  领先集团成员: {group_info['members']}")
    print(f"  平均资金量: {group_info['avg_amount']:.2f}亿")
    
    # 6. 测试综合评判
    print("\n6. 综合评判:")
    total_score, scores = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)
    print(f"  综合得分: {total_score:.2f}")
    print(f"  各项得分: {scores}")
    print(f"  是否发现断层: {'是' if total_score >= thresholds['min_gap_score'] else '否'}")

if __name__ == "__main__":
    test_stock_competition_debug()