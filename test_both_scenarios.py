#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import pandas as pd

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from dynamic_gap_detector import analyze_stock_flow_gap

def test_both_scenarios():
    """测试两种场景：竞争激烈 vs 真正断层"""
    
    print("=== 场景1：竞争激烈（用户的情况） ===")
    
    # 场景1：竞争激烈的情况（用户的实际数据）
    competitive_data = {
        '名称': ['亚太药业', '中电鑫龙', '誉衡药业', '振东制药', '科大讯飞'],
        '今日主力净流入-净额': [2.11e8, 2.02e8, 1.73e8, 1.5e8, 1.2e8]
    }
    
    df_competitive = pd.DataFrame(competitive_data)
    result1 = analyze_stock_flow_gap(df_competitive)
    print(result1)
    
    print("\n" + "="*60)
    print("=== 场景2：明显断层的情况 ===")
    
    # 场景2：存在明显断层的情况
    gap_data = {
        '名称': ['断层龙头', '第二名', '第三名', '第四名', '第五名'],
        '今日主力净流入-净额': [5.0e8, 3.0e8, 2.9e8, 2.8e8, 2.7e8]  # 第1名领先明显
    }
    
    df_gap = pd.DataFrame(gap_data)
    result2 = analyze_stock_flow_gap(df_gap)
    print(result2)
    
    print("\n" + "="*60)
    print("=== 分析总结 ===")
    print("场景1 - 竞争激烈：第1名vs第2名 = 2.11/2.02 = 1.04倍 → 双强争霸")
    print("场景2 - 明显断层：第1名vs第2名 = 5.0/3.0 = 1.67倍 → 应该发现断层")

if __name__ == "__main__":
    test_both_scenarios()