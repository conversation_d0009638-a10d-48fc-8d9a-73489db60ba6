#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import pandas as pd

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from dynamic_gap_detector import analyze_stock_flow_gap

def test_fixed_stock_flow_analysis():
    """测试修复后的个股资金流分析"""
    print("=== 测试修复后的个股资金流分析 ===")
    
    # 创建测试数据：模拟用户的情况
    test_data = {
        '名称': ['亚太药业', '中电鑫龙', '誉衡药业', '振东制药', '科大讯飞', 
                '东方财富', '新易盛', '宁波银行', '赣锋锂业', '万兴科技'],
        '今日主力净流入-净额': [2.11e8, 2.02e8, 1.73e8, 1.5e8, 1.2e8, 
                               1.0e8, 0.9e8, 0.8e8, 0.7e8, 0.6e8]
    }
    
    df_test = pd.DataFrame(test_data)
    
    print("测试数据:")
    for i, row in df_test.iterrows():
        print(f"  {i+1}. {row['名称']}: {row['今日主力净流入-净额']/1e8:.2f}亿")
    
    print("\n执行个股资金流分析...")
    
    # 调用分析函数
    result = analyze_stock_flow_gap(df_test)
    
    print("\n分析结果:")
    print(result)

if __name__ == "__main__":
    test_fixed_stock_flow_analysis()