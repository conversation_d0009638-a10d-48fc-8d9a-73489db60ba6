#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from dynamic_gap_detector import classify_file_type

def test_final_verification():
    """最终验证测试：确认修正后的文件分类正确性"""
    print("=== 最终验证测试 ===")
    
    # 测试个股资金流文件分类
    stock_flow_test_files = [
        "fund_flow_rank_20250725_093047.csv",
        "09-32_fund_flow_tpdog.csv", 
        "09-32_ths_fund_flow.csv",
        "individual_fund_flow_test.csv"
    ]
    
    print("个股资金流文件分类测试:")
    stock_flow_passed = True
    for filename in stock_flow_test_files:
        result = classify_file_type(filename)
        passed = result == 'stock_flow'
        status = "PASS" if passed else "FAIL"
        print(f"  {filename:<40} -> {result:<10} [{status}]")
        if not passed:
            stock_flow_passed = False
    
    # 测试主力资金流文件分类（应该被排除）
    main_fund_files = [
        "main_fund_flow_20250725_093248.csv",
        "main_fund_flow_20250726_100000.csv"
    ]
    
    print("\n主力资金流文件分类测试（应为other）:")
    main_fund_passed = True
    for filename in main_fund_files:
        result = classify_file_type(filename)
        passed = result == 'other'
        status = "PASS" if passed else "FAIL"
        print(f"  {filename:<40} -> {result:<10} [{status}]")
        if not passed:
            main_fund_passed = False
    
    # 测试其他文件类型
    other_files = [
        ("concept_fund_flow_20250725_093248.csv", "concept"),
        ("sector_fund_flow_rank_20250725_093248.csv", "sector")
    ]
    
    print("\n其他文件类型分类测试:")
    other_passed = True
    for filename, expected in other_files:
        result = classify_file_type(filename)
        passed = result == expected
        status = "PASS" if passed else "FAIL"
        print(f"  {filename:<40} -> {result:<10} [{status}]")
        if not passed:
            other_passed = False
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    print(f"个股资金流文件分类: {'通过' if stock_flow_passed else '失败'}")
    print(f"主力资金流文件排除: {'通过' if main_fund_passed else '失败'}")
    print(f"其他文件类型分类: {'通过' if other_passed else '失败'}")
    
    all_passed = stock_flow_passed and main_fund_passed and other_passed
    print(f"整体测试结果: {'全部通过' if all_passed else '存在问题'}")
    
    return all_passed

if __name__ == "__main__":
    test_final_verification()