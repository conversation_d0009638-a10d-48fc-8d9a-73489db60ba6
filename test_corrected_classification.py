#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from dynamic_gap_detector import classify_file_type

def test_corrected_classification():
    """测试修正后的文件分类"""
    print("=== 测试修正后的文件分类 ===")
    
    test_files = [
        "fund_flow_rank_20250725_093047.csv",  # 应该是 stock_flow
        "09-32_fund_flow_tpdog.csv",           # 应该是 stock_flow
        "09-32_ths_fund_flow.csv",             # 应该是 stock_flow
        "main_fund_flow_20250725_093248.csv",  # 应该是 other（不是个股）
        "individual_fund_flow_test.csv",       # 应该是 stock_flow
        "concept_fund_flow_20250725_093248.csv", # 应该是 concept
        "sector_fund_flow_rank_20250725_093248.csv" # 应该是 sector
    ]
    
    print("文件分类测试结果:")
    for filename in test_files:
        file_type = classify_file_type(filename)
        print(f"  {filename:<45} -> {file_type}")
    
    # 检查main_fund_flow是否被正确排除
    main_fund_flow_file = "main_fund_flow_20250725_093248.csv"
    result = classify_file_type(main_fund_flow_file)
    
    if result == 'other':
        print(f"\n✅ 成功：{main_fund_flow_file} 被正确分类为 'other'（不是个股资金流）")
    else:
        print(f"\n❌ 错误：{main_fund_flow_file} 被错误分类为 '{result}'")

if __name__ == "__main__":
    test_corrected_classification()